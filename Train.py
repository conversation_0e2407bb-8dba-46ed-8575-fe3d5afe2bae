import os
import pathlib
import sys

import matplotlib.pyplot as plt
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers
from tensorflow.keras.models import Sequential

from logging_utils import print_error, print_info, print_usage


def train(dataset_path: str):
    data_dir = pathlib.Path(dataset_path)
    image_count = len(list(data_dir.glob('*/*.JPG')))
    print_info(f'Image count: {image_count}')

    batch_size = 64
    img_height = 180
    img_width = 180
    seed = 134

    # CREATE TRAINING SETS
    train_ds = tf.keras.utils.image_dataset_from_directory(
        data_dir,
        validation_split=0.2,
        subset="training",
        seed=seed,
        image_size=(img_height, img_width),
        batch_size=batch_size
    )

    val_ds = tf.keras.utils.image_dataset_from_directory(
        data_dir,
        validation_split=0.2,
        subset="validation",
        seed=seed,
        image_size=(img_height, img_width),
        batch_size=batch_size
    )
    class_names = train_ds.class_names
    print_info(f'Class names: {class_names}')
    # PERFORMANCE
    AUTOTUNE = tf.data.AUTOTUNE

    train_ds = train_ds.cache().shuffle(1000).prefetch(buffer_size=AUTOTUNE)
    val_ds = val_ds.cache().prefetch(buffer_size=AUTOTUNE)

    # STANDARDIZE
    normalization_layer = layers.Rescaling(1. / 255)
    normalized_ds = train_ds.map(lambda x, y: (normalization_layer(x), y))
    image_batch, labels_batch = next(iter(normalized_ds))
    first_image = image_batch[0]
    # Notice the pixel values are now in `[0,1]`.
    print_info(
        f'Pixel value range: {np.min(first_image)} to {np.max(first_image)}')

    # GENERATE MODEL
    num_classes = len(class_names)

    model = Sequential([
        layers.Rescaling(1. / 255, input_shape=(img_height, img_width, 3)),
        layers.Conv2D(16, 3, padding='same', activation='relu'),
        layers.MaxPooling2D(),
        layers.Conv2D(32, 3, padding='same', activation='relu'),
        layers.MaxPooling2D(),
        layers.Conv2D(64, 3, padding='same', activation='relu'),
        layers.MaxPooling2D(),
        layers.Flatten(),
        layers.Dense(128, activation='relu'),
        layers.Dense(num_classes)
    ])
    model.compile(optimizer='adam',
                  loss=tf.keras.losses.SparseCategoricalCrossentropy(
                      from_logits=True),
                  metrics=['accuracy']
                  )
    model.summary()

    # TRAIN MODEL
    epochs = 15
    history = model.fit(
        train_ds,
        validation_data=val_ds,
        epochs=epochs
    )

    # SAVE
    # Get name for file
    dataset_name = dataset_path.removesuffix('/').split('/')[-1]
    # Convert the model.
    converter = tf.lite.TFLiteConverter.from_keras_model(model)
    tflite_model = converter.convert()

    # create folder "./training-results" if doesnt exists
    if not os.path.exists('training-results'):
        os.makedirs('training-results')

    # Save the model.
    with open(f'training-results/model-{dataset_name}.tflite', 'wb') as f:
        f.write(tflite_model)
    # Save the class names
    with open(f'training-results/model-{dataset_name}-classes.txt', 'w') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")

    # VISUALIZE
    acc = history.history['accuracy']
    val_acc = history.history['val_accuracy']

    loss = history.history['loss']
    val_loss = history.history['val_loss']

    epochs_range = range(epochs)

    plt.figure(figsize=(8, 8))
    plt.subplot(1, 2, 1)
    plt.plot(epochs_range, acc, label='Training Accuracy')
    plt.plot(epochs_range, val_acc, label='Validation Accuracy')
    plt.legend(loc='lower right')
    plt.title('Training and Validation Accuracy ' + dataset_name)

    plt.subplot(1, 2, 2)
    plt.plot(epochs_range, loss, label='Training Loss')
    plt.plot(epochs_range, val_loss, label='Validation Loss')
    plt.legend(loc='upper right')
    plt.title('Training and Validation Loss')
    plt.show()


if __name__ == "__main__":
    if len(sys.argv) < 2 or (
            len(sys.argv) == 3 and sys.argv[2] != '-A') or len(
            sys.argv) >= 3:
        print_usage("Usage: python Train.py <dataset_folder> [-A]")
        sys.exit(1)

    dataset_path = sys.argv[1]
    if not os.path.isdir(dataset_path):
        print_error(
            f"The provided path '{dataset_path}' is not a valid directory.")
        sys.exit(1)

    train(dataset_path)
