"""
Shared logging utility module for consistent colored output across all files.

This module provides colored logging functions for error, info, and warning
messages to ensure consistent formatting and colors throughout the project:
- [ERROR] messages in red
- [INFO] messages in green
- [WARNING] messages in yellow
"""


# ANSI color codes for colored terminal output
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'  # End color


def print_error(message):
    """Print error message in red color."""
    print(
        f"{Colors.RED}{Colors.BOLD}[ERROR]{Colors.END}" +
        f" {Colors.RED}{message}{Colors.END}")


def print_info(message):
    """Print info message in green color."""
    print(
        f"{Colors.GREEN}{Colors.BOLD}[INFO]{Colors.END}" +
        f" {Colors.GREEN}{message}{Colors.END}")


def print_warning(message):
    """Print warning message in yellow color."""
    print(
        f"{Colors.YELLOW}{Colors.BOLD}[WARNING]{Colors.END}" +
        f" {Colors.YELLOW}{message}{Colors.END}")
