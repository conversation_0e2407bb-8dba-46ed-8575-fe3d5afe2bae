import os
import sys

import numpy as np
import skimage as ski

from logging_utils import print_error, print_info, print_usage


def blur(image, sigma=3.0):
    new_image = ski.filters.gaussian(image, sigma=(sigma, sigma), truncate=3.5,
                                     channel_axis=-1)
    return ski.util.img_as_ubyte(new_image)


def illumination(image, factor=0.5):
    new_image = ski.exposure.adjust_gamma(image, gamma=0.3)
    return ski.util.img_as_ubyte(new_image)


def rotate(image, angle=15):
    new_image = ski.transform.rotate(image, angle, resize=True)
    return ski.util.img_as_ubyte(new_image)


def contrast(image, contrast=200, brightness=0):
    new_image = image * (contrast / 127 + 1) - contrast + brightness
    new_image[np.where(new_image > 255)] = 255
    new_image /= 255
    return ski.util.img_as_ubyte(new_image)


def scaling(image):
    matrix = [
        [0.8, 0.0, 0.0],
        [0.0, 0.8, 0.0],
        [0.0, 0.0, 1]
    ]
    tform = ski.transform.ProjectiveTransform(matrix)
    new_image = ski.transform.warp(image, tform)
    return ski.util.img_as_ubyte(new_image)


def skew(image):
    matrix = [
        [0.8, 0.7, 0.0],
        [0.0, 1, 0.0],
        [0.0, 0.0, 1]
    ]
    tform = ski.transform.SimilarityTransform(scale=1.2,
                                              translation=(-250, -50))
    new_image = ski.transform.warp(image, tform)
    tform = ski.transform.ProjectiveTransform(matrix)
    new_image = ski.transform.warp(new_image, tform)
    return ski.util.img_as_ubyte(new_image)


functions = {
    "blur": blur,
    "illumination": illumination,
    "rotate": rotate,
    "contrast": contrast,
    "scaling": scaling,
    "skew": skew
}


def apply_augmentation_to_one_image(image_path):
    # Get the directory and base name of the input image
    base_dir = os.path.dirname(image_path)
    base_name = os.path.basename(image_path)

    base_name, ext = os.path.splitext(base_name)

    image = ski.io.imread(image_path)

    # Apply each augmentation function
    for augmentation_name, func in functions.items():
        new_image = func(image)
        new_image_path = os.path.join(base_dir,
                                      f"{base_name}_{augmentation_name}.png")
        ski.io.imsave(new_image_path, new_image)
        print_info(f"Saved: {new_image_path}")


def apply_augmentations(path: str):
    """Apply augmentations to an image or all images in a directory."""
    if os.path.isdir(path):
        # Process all images in the directory
        for file_name in os.listdir(path):
            file_path = os.path.join(path, file_name)
            apply_augmentations(file_path)
    elif os.path.isfile(path) and path.lower().endswith(
            ('.png', '.jpg', '.jpeg')):
        print_info(f"Processing {path}")
        apply_augmentation_to_one_image(path)
    else:
        print_error("The provided path is neither a file nor a directory.")


def balance_dataset(directory: str):
    # Delete files to balance the number of pictures in each subdirectory
    files_amounts = [
        len(os.listdir(os.path.join(directory, subdir))) for subdir in
        os.listdir(directory)
    ]
    min_amount = min(files_amounts)
    for subdir in os.listdir(directory):
        subdir_path = os.path.join(directory, subdir)
        if os.path.isdir(subdir_path):
            files = os.listdir(subdir_path)
            if len(files) > min_amount:
                print(
                    f"Removing {len(files) - min_amount}" +
                    f"files from '{subdir_path}'")
                for file in files[min_amount:]:
                    os.remove(os.path.join(subdir_path, file))


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print_usage("Usage: python3 Augmentation.py <image_or_directory_path>")
        exit(1)
    input_path = sys.argv[1]

    make_the_same_amount_of_files = os.path.isdir(input_path)
    apply_augmentations(input_path)

    if make_the_same_amount_of_files:
        balance_dataset(input_path)
