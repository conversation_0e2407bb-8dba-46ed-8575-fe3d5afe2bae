import os
import sys

import tensorflow as tf
from ai_edge_litert.interpreter import Interpreter


def getTrainedModels():
    trained_models = []
    if not os.path.exists("training-results"):
        return trained_models
    for file in os.listdir("training-results"):
        if file.startswith("model-") and file.endswith(".tflite"):
            trained_models.append(file[6:-7])
    return trained_models


def getModelFromPath(param, models):
    for model in models:
        if ('/' + model + '/') in param:
            model_path = os.path.join("training-results",
                                      f"model-{model}.tflite")
            classes_path = os.path.join("training-results",
                                        f"model-{model}-classes.txt")
            if not os.path.isfile(model_path) or not os.path.isfile(
                    classes_path):
                print(
                    "[ERROR] Model or classes file not found for model:" +
                    f"{model}")
                return None
            classes = []
            with open(classes_path, 'r') as f:
                classes = [line.strip() for line in f.readlines()]
            print(
                f"[INFO] Found model: {model} and classes:" +
                f" model-{model}-classes.txt")
            return model_path, classes
    return None, None


def predict(model_path, classes, image_path):
    # Load and preprocess the image
    img = tf.io.read_file(image_path)
    img = tf.image.decode_image(img, channels=3)
    img = tf.image.resize(img, [180, 180])  # Use your model's input size
    img = tf.expand_dims(img, axis=0)  # Add batch dimension

    # Run prediction
    interpreter = Interpreter(model_path=model_path)
    classify_lite = interpreter.get_signature_runner('serving_default')
    predictions_lite = classify_lite(keras_tensor=img)['output_0']

    # Get the predicted class
    score_lite = tf.nn.softmax(predictions_lite)
    predicted_class = tf.argmax(score_lite, axis=1).numpy()[0]
    print(f"Predicted class: {classes[predicted_class]}")


def get_model_from_name(param, models):
    print(param)
    for model in models:
        if param in model:
            model_path = os.path.join("training-results",
                                      f"model-{model}.tflite")
            classes_path = os.path.join("training-results",
                                        f"model-{model}-classes.txt")
            if not os.path.isfile(model_path) or not os.path.isfile(
                    classes_path):
                print(
                    "[ERROR] Model or classes file not found for model:" +
                    f"{model}")
                return None
            classes = []
            with open(classes_path, 'r') as f:
                classes = [line.strip() for line in f.readlines()]
            print(
                f"[INFO] Found model: {model} and classes:" +
                f" model-{model}-classes.txt")
            return model_path, classes
    print(f"[ERROR] Model '{param}' not found in trained models.")
    print(f"[ERROR] Available models: {', '.join(models)}")
    return None, None


def predict_folder(model, classes, path):
    for file in os.listdir(path):
        file_path = os.path.join(path, file)
        if os.path.isfile(file_path):
            print(f"[INFO] Predicting {file_path}")
            predict(model, classes, file_path)
        elif os.path.isdir(file_path):
            predict_folder(model, classes, file_path)


if __name__ == "__main__":
    if not (len(sys.argv) == 2 or (
            len(sys.argv) == 4 and sys.argv[1] == '-m') or '-l' in sys.argv):
        print("Usage: python Train.py [-m model_name] [-l] <path_to_image>")
        sys.exit(1)

    # FIND MODEL
    models = getTrainedModels()
    if not models:
        print("No trained model found.")
        sys.exit(404)

    # List trained models
    if '-l' in sys.argv:
        print("Trained models:")
        for model in models:
            print(f"- {model}")
        sys.exit(0)

    # If training model specified by name
    if len(sys.argv) == 4 and sys.argv[1] == '-m':
        model, classes = get_model_from_name(sys.argv[2], models)
        # remove the -m flag from sys.argv
        sys.argv.pop(1)
        sys.argv.pop(1)
    else:
        model, classes = getModelFromPath(sys.argv[1], models)
    if model is None:
        sys.exit(404)

    if not os.path.isfile(sys.argv[1]) and not os.path.isdir(sys.argv[1]):
        print(f"[ERROR] The provided path '{sys.argv[1]}'" +
              " is not a valid file or a folder.")
        sys.exit(500)

    if os.path.isdir(sys.argv[1]):
        predict_folder(model, classes, sys.argv[1])
    else:
        predict(model, classes, sys.argv[1])
