Metadata-Version: 2.1
Name: pyflakes
Version: 3.4.0
Summary: passive checker of Python programs
Home-page: https://github.com/PyCQA/pyflakes
Author: A lot of people
Author-email: <EMAIL>
License: MIT
Classifier: Development Status :: 6 - Mature
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development
Classifier: Topic :: Utilities
Requires-Python: >=3.9
License-File: LICENSE

========
Pyflakes
========

A simple program which checks Python source files for errors.

Pyflakes analyzes programs and detects various errors.  It works by
parsing the source file, not importing it, so it is safe to use on
modules with side effects.  It's also much faster.

It is `available on PyPI <https://pypi.org/project/pyflakes/>`_
and it supports all active versions of Python: 3.9+.



Installation
------------

It can be installed with::

  $ pip install --upgrade pyflakes


Useful tips:

* Be sure to install it for a version of Python which is compatible
  with your codebase: ``python#.# -m pip install pyflakes`` (for example,
  ``python3.10 -m pip install pyflakes``)

* You can also invoke Pyflakes with ``python#.# -m pyflakes .`` if you want
  to run it for a specific python version.

* If you require more options and more flexibility, you could give a
  look to Flake8_ too.


Design Principles
-----------------
Pyflakes makes a simple promise: it will never complain about style,
and it will try very, very hard to never emit false positives.

Pyflakes is also faster than Pylint_. This is
largely because Pyflakes only examines the syntax tree of each file
individually. As a consequence, Pyflakes is more limited in the
types of things it can check.

If you like Pyflakes but also want stylistic checks, you want
flake8_, which combines
Pyflakes with style checks against
`PEP 8`_ and adds
per-project configuration ability.


Mailing-list
------------

Share your feedback and ideas: `subscribe to the mailing-list
<https://mail.python.org/mailman/listinfo/code-quality>`_

Contributing
------------

Issues are tracked on `GitHub <https://github.com/PyCQA/pyflakes/issues>`_.

Patches may be submitted via a `GitHub pull request`_.
If you are comfortable doing so, please `rebase your changes`_
so they may be applied to main with a fast-forward merge, and each commit is
a coherent unit of work with a well-written log message.  If you are not
comfortable with this rebase workflow, the project maintainers will be happy to
rebase your commits for you.

All changes should include tests and pass flake8_.

.. image:: https://github.com/PyCQA/pyflakes/workflows/Test/badge.svg
   :target: https://github.com/PyCQA/pyflakes/actions
   :alt: GitHub Actions build status

.. _Pylint: https://pylint.pycqa.org/
.. _flake8: https://pypi.org/project/flake8/
.. _`PEP 8`: https://www.python.org/dev/peps/pep-0008/
.. _`rebase your changes`: https://git-scm.com/book/en/v2/Git-Branching-Rebasing
.. _`GitHub pull request`: https://github.com/PyCQA/pyflakes/pulls

Changelog
---------

Please see `NEWS.rst <https://github.com/PyCQA/pyflakes/blob/main/NEWS.rst>`_.
