# Copyright 2018 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
# pylint: disable=line-too-long
"""List of renames to apply when converting from TF 1.0 to TF 2.0.

THIS FILE IS AUTOGENERATED: To update, please run:
  bazel run tensorflow/tools/compatibility/update:generate_v2_renames_map
This file should be updated whenever endpoints are deprecated.
"""
renames = {
    'tf.AUTO_REUSE':
        'tf.compat.v1.AUTO_REUSE',
    'tf.AttrValue':
        'tf.compat.v1.AttrValue',
    'tf.COMPILER_VERSION':
        'tf.version.COMPILER_VERSION',
    'tf.CXX11_ABI_FLAG':
        'tf.sysconfig.CXX11_ABI_FLAG',
    'tf.CXX_VERSION':
        'tf.sysconfig.CXX_VERSION',
    'tf.ConditionalAccumulator':
        'tf.compat.v1.ConditionalAccumulator',
    'tf.ConditionalAccumulatorBase':
        'tf.compat.v1.ConditionalAccumulatorBase',
    'tf.ConfigProto':
        'tf.compat.v1.ConfigProto',
    'tf.Dimension':
        'tf.compat.v1.Dimension',
    'tf.Event':
        'tf.compat.v1.Event',
    'tf.FIFOQueue':
        'tf.queue.FIFOQueue',
    'tf.FixedLenFeature':
        'tf.io.FixedLenFeature',
    'tf.FixedLenSequenceFeature':
        'tf.io.FixedLenSequenceFeature',
    'tf.FixedLengthRecordReader':
        'tf.compat.v1.FixedLengthRecordReader',
    'tf.GIT_VERSION':
        'tf.version.GIT_VERSION',
    'tf.GPUOptions':
        'tf.compat.v1.GPUOptions',
    'tf.GRAPH_DEF_VERSION':
        'tf.version.GRAPH_DEF_VERSION',
    'tf.GRAPH_DEF_VERSION_MIN_CONSUMER':
        'tf.version.GRAPH_DEF_VERSION_MIN_CONSUMER',
    'tf.GRAPH_DEF_VERSION_MIN_PRODUCER':
        'tf.version.GRAPH_DEF_VERSION_MIN_PRODUCER',
    'tf.GraphDef':
        'tf.compat.v1.GraphDef',
    'tf.GraphKeys':
        'tf.compat.v1.GraphKeys',
    'tf.GraphOptions':
        'tf.compat.v1.GraphOptions',
    'tf.HistogramProto':
        'tf.compat.v1.HistogramProto',
    'tf.IdentityReader':
        'tf.compat.v1.IdentityReader',
    'tf.InteractiveSession':
        'tf.compat.v1.InteractiveSession',
    'tf.LMDBReader':
        'tf.compat.v1.LMDBReader',
    'tf.LogMessage':
        'tf.compat.v1.LogMessage',
    'tf.MONOLITHIC_BUILD':
        'tf.sysconfig.MONOLITHIC_BUILD',
    'tf.MetaGraphDef':
        'tf.compat.v1.MetaGraphDef',
    'tf.NameAttrList':
        'tf.compat.v1.NameAttrList',
    'tf.NoGradient':
        'tf.no_gradient',
    'tf.NodeDef':
        'tf.compat.v1.NodeDef',
    'tf.NotDifferentiable':
        'tf.no_gradient',
    'tf.OpError':
        'tf.errors.OpError',
    'tf.OptimizerOptions':
        'tf.compat.v1.OptimizerOptions',
    'tf.PaddingFIFOQueue':
        'tf.queue.PaddingFIFOQueue',
    'tf.Print':
        'tf.compat.v1.Print',
    'tf.PriorityQueue':
        'tf.queue.PriorityQueue',
    'tf.QUANTIZED_DTYPES':
        'tf.dtypes.QUANTIZED_DTYPES',
    'tf.QueueBase':
        'tf.queue.QueueBase',
    'tf.RandomShuffleQueue':
        'tf.queue.RandomShuffleQueue',
    'tf.ReaderBase':
        'tf.compat.v1.ReaderBase',
    'tf.RunMetadata':
        'tf.compat.v1.RunMetadata',
    'tf.RunOptions':
        'tf.compat.v1.RunOptions',
    'tf.Session':
        'tf.compat.v1.Session',
    'tf.SessionLog':
        'tf.compat.v1.SessionLog',
    'tf.SparseConditionalAccumulator':
        'tf.compat.v1.SparseConditionalAccumulator',
    'tf.SparseFeature':
        'tf.io.SparseFeature',
    'tf.SparseTensorValue':
        'tf.compat.v1.SparseTensorValue',
    'tf.Summary':
        'tf.compat.v1.Summary',
    'tf.SummaryMetadata':
        'tf.compat.v1.SummaryMetadata',
    'tf.TFRecordReader':
        'tf.compat.v1.TFRecordReader',
    'tf.TensorInfo':
        'tf.compat.v1.TensorInfo',
    'tf.TextLineReader':
        'tf.compat.v1.TextLineReader',
    'tf.VERSION':
        'tf.version.VERSION',
    'tf.VarLenFeature':
        'tf.io.VarLenFeature',
    'tf.VariableScope':
        'tf.compat.v1.VariableScope',
    'tf.WholeFileReader':
        'tf.compat.v1.WholeFileReader',
    'tf.accumulate_n':
        'tf.math.accumulate_n',
    'tf.add_check_numerics_ops':
        'tf.compat.v1.add_check_numerics_ops',
    'tf.add_to_collection':
        'tf.compat.v1.add_to_collection',
    'tf.add_to_collections':
        'tf.compat.v1.add_to_collections',
    'tf.all_variables':
        'tf.compat.v1.all_variables',
    'tf.angle':
        'tf.math.angle',
    'tf.app.run':
        'tf.compat.v1.app.run',
    'tf.assert_proper_iterable':
        'tf.debugging.assert_proper_iterable',
    'tf.assert_same_float_dtype':
        'tf.debugging.assert_same_float_dtype',
    'tf.assign':
        'tf.compat.v1.assign',
    'tf.assign_add':
        'tf.compat.v1.assign_add',
    'tf.assign_sub':
        'tf.compat.v1.assign_sub',
    'tf.batch_scatter_update':
        'tf.compat.v1.batch_scatter_update',
    'tf.betainc':
        'tf.math.betainc',
    'tf.ceil':
        'tf.math.ceil',
    'tf.check_numerics':
        'tf.debugging.check_numerics',
    'tf.cholesky':
        'tf.linalg.cholesky',
    'tf.cholesky_solve':
        'tf.linalg.cholesky_solve',
    'tf.clip_by_average_norm':
        'tf.compat.v1.clip_by_average_norm',
    'tf.colocate_with':
        'tf.compat.v1.colocate_with',
    'tf.conj':
        'tf.math.conj',
    'tf.container':
        'tf.compat.v1.container',
    'tf.control_flow_v2_enabled':
        'tf.compat.v1.control_flow_v2_enabled',
    'tf.convert_to_tensor_or_indexed_slices':
        'tf.compat.v1.convert_to_tensor_or_indexed_slices',
    'tf.convert_to_tensor_or_sparse_tensor':
        'tf.compat.v1.convert_to_tensor_or_sparse_tensor',
    'tf.count_up_to':
        'tf.compat.v1.count_up_to',
    'tf.create_partitioned_variables':
        'tf.compat.v1.create_partitioned_variables',
    'tf.cross':
        'tf.linalg.cross',
    'tf.cumprod':
        'tf.math.cumprod',
    'tf.data.get_output_classes':
        'tf.compat.v1.data.get_output_classes',
    'tf.data.get_output_shapes':
        'tf.compat.v1.data.get_output_shapes',
    'tf.data.get_output_types':
        'tf.compat.v1.data.get_output_types',
    'tf.data.make_initializable_iterator':
        'tf.compat.v1.data.make_initializable_iterator',
    'tf.data.make_one_shot_iterator':
        'tf.compat.v1.data.make_one_shot_iterator',
    'tf.debugging.is_finite':
        'tf.math.is_finite',
    'tf.debugging.is_inf':
        'tf.math.is_inf',
    'tf.debugging.is_nan':
        'tf.math.is_nan',
    'tf.debugging.is_non_decreasing':
        'tf.math.is_non_decreasing',
    'tf.debugging.is_strictly_increasing':
        'tf.math.is_strictly_increasing',
    'tf.decode_base64':
        'tf.io.decode_base64',
    'tf.decode_compressed':
        'tf.io.decode_compressed',
    'tf.decode_json_example':
        'tf.io.decode_json_example',
    'tf.delete_session_tensor':
        'tf.compat.v1.delete_session_tensor',
    'tf.depth_to_space':
        'tf.nn.depth_to_space',
    'tf.dequantize':
        'tf.quantization.dequantize',
    'tf.deserialize_many_sparse':
        'tf.io.deserialize_many_sparse',
    'tf.diag':
        'tf.linalg.tensor_diag',
    'tf.diag_part':
        'tf.linalg.tensor_diag_part',
    'tf.digamma':
        'tf.math.digamma',
    'tf.dimension_at_index':
        'tf.compat.dimension_at_index',
    'tf.dimension_value':
        'tf.compat.dimension_value',
    'tf.disable_control_flow_v2':
        'tf.compat.v1.disable_control_flow_v2',
    'tf.disable_eager_execution':
        'tf.compat.v1.disable_eager_execution',
    'tf.disable_resource_variables':
        'tf.compat.v1.disable_resource_variables',
    'tf.disable_tensor_equality':
        'tf.compat.v1.disable_tensor_equality',
    'tf.disable_v2_behavior':
        'tf.compat.v1.disable_v2_behavior',
    'tf.disable_v2_tensorshape':
        'tf.compat.v1.disable_v2_tensorshape',
    'tf.distribute.get_loss_reduction':
        'tf.compat.v1.distribute.get_loss_reduction',
    'tf.distributions.Bernoulli':
        'tf.compat.v1.distributions.Bernoulli',
    'tf.distributions.Beta':
        'tf.compat.v1.distributions.Beta',
    'tf.distributions.Categorical':
        'tf.compat.v1.distributions.Categorical',
    'tf.distributions.Dirichlet':
        'tf.compat.v1.distributions.Dirichlet',
    'tf.distributions.DirichletMultinomial':
        'tf.compat.v1.distributions.DirichletMultinomial',
    'tf.distributions.Distribution':
        'tf.compat.v1.distributions.Distribution',
    'tf.distributions.Exponential':
        'tf.compat.v1.distributions.Exponential',
    'tf.distributions.FULLY_REPARAMETERIZED':
        'tf.compat.v1.distributions.FULLY_REPARAMETERIZED',
    'tf.distributions.Gamma':
        'tf.compat.v1.distributions.Gamma',
    'tf.distributions.Laplace':
        'tf.compat.v1.distributions.Laplace',
    'tf.distributions.Multinomial':
        'tf.compat.v1.distributions.Multinomial',
    'tf.distributions.NOT_REPARAMETERIZED':
        'tf.compat.v1.distributions.NOT_REPARAMETERIZED',
    'tf.distributions.Normal':
        'tf.compat.v1.distributions.Normal',
    'tf.distributions.RegisterKL':
        'tf.compat.v1.distributions.RegisterKL',
    'tf.distributions.ReparameterizationType':
        'tf.compat.v1.distributions.ReparameterizationType',
    'tf.distributions.StudentT':
        'tf.compat.v1.distributions.StudentT',
    'tf.distributions.Uniform':
        'tf.compat.v1.distributions.Uniform',
    'tf.distributions.kl_divergence':
        'tf.compat.v1.distributions.kl_divergence',
    'tf.div':
        'tf.compat.v1.div',
    'tf.div_no_nan':
        'tf.math.divide_no_nan',
    'tf.dtypes.as_string':
        'tf.strings.as_string',
    'tf.enable_control_flow_v2':
        'tf.compat.v1.enable_control_flow_v2',
    'tf.enable_eager_execution':
        'tf.compat.v1.enable_eager_execution',
    'tf.enable_resource_variables':
        'tf.compat.v1.enable_resource_variables',
    'tf.enable_tensor_equality':
        'tf.compat.v1.enable_tensor_equality',
    'tf.enable_v2_behavior':
        'tf.compat.v1.enable_v2_behavior',
    'tf.enable_v2_tensorshape':
        'tf.compat.v1.enable_v2_tensorshape',
    'tf.encode_base64':
        'tf.io.encode_base64',
    'tf.erf':
        'tf.math.erf',
    'tf.erfc':
        'tf.math.erfc',
    'tf.executing_eagerly_outside_functions':
        'tf.compat.v1.executing_eagerly_outside_functions',
    'tf.experimental.output_all_intermediates':
        'tf.compat.v1.experimental.output_all_intermediates',
    'tf.expm1':
        'tf.math.expm1',
    'tf.fake_quant_with_min_max_args':
        'tf.quantization.fake_quant_with_min_max_args',
    'tf.fake_quant_with_min_max_args_gradient':
        'tf.quantization.fake_quant_with_min_max_args_gradient',
    'tf.fake_quant_with_min_max_vars':
        'tf.quantization.fake_quant_with_min_max_vars',
    'tf.fake_quant_with_min_max_vars_gradient':
        'tf.quantization.fake_quant_with_min_max_vars_gradient',
    'tf.fake_quant_with_min_max_vars_per_channel':
        'tf.quantization.fake_quant_with_min_max_vars_per_channel',
    'tf.fake_quant_with_min_max_vars_per_channel_gradient':
        'tf.quantization.fake_quant_with_min_max_vars_per_channel_gradient',
    'tf.feature_column.input_layer':
        'tf.compat.v1.feature_column.input_layer',
    'tf.feature_column.linear_model':
        'tf.compat.v1.feature_column.linear_model',
    'tf.feature_column.shared_embedding_columns':
        'tf.compat.v1.feature_column.shared_embedding_columns',
    'tf.fft':
        'tf.signal.fft',
    'tf.fft2d':
        'tf.signal.fft2d',
    'tf.fft3d':
        'tf.signal.fft3d',
    'tf.fixed_size_partitioner':
        'tf.compat.v1.fixed_size_partitioner',
    'tf.floordiv':
        'tf.math.floordiv',
    'tf.floormod':
        'tf.math.floormod',
    'tf.get_collection':
        'tf.compat.v1.get_collection',
    'tf.get_collection_ref':
        'tf.compat.v1.get_collection_ref',
    'tf.get_default_graph':
        'tf.compat.v1.get_default_graph',
    'tf.get_default_session':
        'tf.compat.v1.get_default_session',
    'tf.get_local_variable':
        'tf.compat.v1.get_local_variable',
    'tf.get_seed':
        'tf.compat.v1.get_seed',
    'tf.get_session_handle':
        'tf.compat.v1.get_session_handle',
    'tf.get_session_tensor':
        'tf.compat.v1.get_session_tensor',
    'tf.get_variable':
        'tf.compat.v1.get_variable',
    'tf.get_variable_scope':
        'tf.compat.v1.get_variable_scope',
    'tf.gfile.FastGFile':
        'tf.compat.v1.gfile.FastGFile',
    'tf.global_norm':
        'tf.linalg.global_norm',
    'tf.global_variables':
        'tf.compat.v1.global_variables',
    'tf.global_variables_initializer':
        'tf.compat.v1.global_variables_initializer',
    'tf.graph_util.convert_variables_to_constants':
        'tf.compat.v1.graph_util.convert_variables_to_constants',
    'tf.graph_util.extract_sub_graph':
        'tf.compat.v1.graph_util.extract_sub_graph',
    'tf.graph_util.must_run_on_cpu':
        'tf.compat.v1.graph_util.must_run_on_cpu',
    'tf.graph_util.remove_training_nodes':
        'tf.compat.v1.graph_util.remove_training_nodes',
    'tf.graph_util.tensor_shape_from_node_def_name':
        'tf.compat.v1.graph_util.tensor_shape_from_node_def_name',
    'tf.ifft':
        'tf.signal.ifft',
    'tf.ifft2d':
        'tf.signal.ifft2d',
    'tf.ifft3d':
        'tf.signal.ifft3d',
    'tf.igamma':
        'tf.math.igamma',
    'tf.igammac':
        'tf.math.igammac',
    'tf.imag':
        'tf.math.imag',
    'tf.image.resize_area':
        'tf.compat.v1.image.resize_area',
    'tf.image.resize_bicubic':
        'tf.compat.v1.image.resize_bicubic',
    'tf.image.resize_bilinear':
        'tf.compat.v1.image.resize_bilinear',
    'tf.image.resize_image_with_crop_or_pad':
        'tf.image.resize_with_crop_or_pad',
    'tf.image.resize_image_with_pad':
        'tf.compat.v1.image.resize_image_with_pad',
    'tf.image.resize_nearest_neighbor':
        'tf.compat.v1.image.resize_nearest_neighbor',
    'tf.image.transpose_image':
        'tf.image.transpose',
    'tf.initialize_all_tables':
        'tf.compat.v1.initialize_all_tables',
    'tf.initialize_all_variables':
        'tf.compat.v1.initialize_all_variables',
    'tf.initialize_local_variables':
        'tf.compat.v1.initialize_local_variables',
    'tf.initialize_variables':
        'tf.compat.v1.initialize_variables',
    'tf.initializers.global_variables':
        'tf.compat.v1.initializers.global_variables',
    'tf.initializers.local_variables':
        'tf.compat.v1.initializers.local_variables',
    'tf.initializers.tables_initializer':
        'tf.compat.v1.initializers.tables_initializer',
    'tf.initializers.uniform_unit_scaling':
        'tf.compat.v1.initializers.uniform_unit_scaling',
    'tf.initializers.variables':
        'tf.compat.v1.initializers.variables',
    'tf.invert_permutation':
        'tf.math.invert_permutation',
    'tf.io.PaddingFIFOQueue':
        'tf.queue.PaddingFIFOQueue',
    'tf.io.PriorityQueue':
        'tf.queue.PriorityQueue',
    'tf.io.QueueBase':
        'tf.queue.QueueBase',
    'tf.io.RandomShuffleQueue':
        'tf.queue.RandomShuffleQueue',
    'tf.io.TFRecordCompressionType':
        'tf.compat.v1.io.TFRecordCompressionType',
    'tf.io.tf_record_iterator':
        'tf.compat.v1.io.tf_record_iterator',
    'tf.is_finite':
        'tf.math.is_finite',
    'tf.is_inf':
        'tf.math.is_inf',
    'tf.is_nan':
        'tf.math.is_nan',
    'tf.is_non_decreasing':
        'tf.math.is_non_decreasing',
    'tf.is_numeric_tensor':
        'tf.debugging.is_numeric_tensor',
    'tf.is_strictly_increasing':
        'tf.math.is_strictly_increasing',
    'tf.is_variable_initialized':
        'tf.compat.v1.is_variable_initialized',
    'tf.keras.backend.get_session':
        'tf.compat.v1.keras.backend.get_session',
    'tf.keras.backend.set_session':
        'tf.compat.v1.keras.backend.set_session',
    'tf.keras.layers.CuDNNGRU':
        'tf.compat.v1.keras.layers.CuDNNGRU',
    'tf.keras.layers.CuDNNLSTM':
        'tf.compat.v1.keras.layers.CuDNNLSTM',
    'tf.keras.layers.disable_v2_dtype_behavior':
        'tf.compat.v1.keras.layers.disable_v2_dtype_behavior',
    'tf.keras.layers.enable_v2_dtype_behavior':
        'tf.compat.v1.keras.layers.enable_v2_dtype_behavior',
    'tf.keras.losses.cosine':
        'tf.keras.losses.cosine_similarity',
    'tf.keras.losses.cosine_proximity':
        'tf.keras.losses.cosine_similarity',
    'tf.keras.metrics.cosine':
        'tf.keras.losses.cosine_similarity',
    'tf.keras.metrics.cosine_proximity':
        'tf.keras.losses.cosine_similarity',
    'tf.keras.models.LinearModel':
        'tf.keras.experimental.LinearModel',
    'tf.keras.models.WideDeepModel':
        'tf.keras.experimental.WideDeepModel',
    'tf.keras.optimizers.Adadelta':
        'tf.keras.optimizers.legacy.Adadelta',
    'tf.keras.optimizers.Adagrad':
        'tf.keras.optimizers.legacy.Adagrad',
    'tf.keras.optimizers.Adam':
        'tf.keras.optimizers.legacy.Adam',
    'tf.keras.optimizers.Adamax':
        'tf.keras.optimizers.legacy.Adamax',
    'tf.keras.optimizers.Ftrl':
        'tf.keras.optimizers.legacy.Ftrl',
    'tf.keras.optimizers.Nadam':
        'tf.keras.optimizers.legacy.Nadam',
    'tf.keras.optimizers.Optimizer':
        'tf.keras.optimizers.legacy.Optimizer',
    'tf.keras.optimizers.RMSprop':
        'tf.keras.optimizers.legacy.RMSprop',
    'tf.keras.optimizers.SGD':
        'tf.keras.optimizers.legacy.SGD',
    'tf.keras.utils.DeterministicRandomTestTool':
        'tf.compat.v1.keras.utils.DeterministicRandomTestTool',
    'tf.keras.utils.get_or_create_layer':
        'tf.compat.v1.keras.utils.get_or_create_layer',
    'tf.keras.utils.track_tf1_style_variables':
        'tf.compat.v1.keras.utils.track_tf1_style_variables',
    'tf.layers.BatchNormalization':
        'tf.compat.v1.layers.BatchNormalization',
    'tf.layers.InputSpec':
        'tf.keras.layers.InputSpec',
    'tf.layers.batch_normalization':
        'tf.compat.v1.layers.batch_normalization',
    'tf.lbeta':
        'tf.math.lbeta',
    'tf.lgamma':
        'tf.math.lgamma',
    'tf.lin_space':
        'tf.linspace',
    'tf.linalg.transpose':
        'tf.linalg.matrix_transpose',
    'tf.lite.OpHint':
        'tf.compat.v1.lite.OpHint',
    'tf.lite.TocoConverter':
        'tf.compat.v1.lite.TocoConverter',
    'tf.lite.constants.GRAPHVIZ_DOT':
        'tf.compat.v1.lite.constants.GRAPHVIZ_DOT',
    'tf.lite.constants.TFLITE':
        'tf.compat.v1.lite.constants.TFLITE',
    'tf.lite.experimental.convert_op_hints_to_stubs':
        'tf.compat.v1.lite.experimental.convert_op_hints_to_stubs',
    'tf.lite.toco_convert':
        'tf.compat.v1.lite.toco_convert',
    'tf.local_variables':
        'tf.compat.v1.local_variables',
    'tf.local_variables_initializer':
        'tf.compat.v1.local_variables_initializer',
    'tf.log':
        'tf.math.log',
    'tf.log1p':
        'tf.math.log1p',
    'tf.log_sigmoid':
        'tf.math.log_sigmoid',
    'tf.logging.DEBUG':
        'tf.compat.v1.logging.DEBUG',
    'tf.logging.ERROR':
        'tf.compat.v1.logging.ERROR',
    'tf.logging.FATAL':
        'tf.compat.v1.logging.FATAL',
    'tf.logging.INFO':
        'tf.compat.v1.logging.INFO',
    'tf.logging.TaskLevelStatusMessage':
        'tf.compat.v1.logging.TaskLevelStatusMessage',
    'tf.logging.WARN':
        'tf.compat.v1.logging.WARN',
    'tf.logging.debug':
        'tf.compat.v1.logging.debug',
    'tf.logging.error':
        'tf.compat.v1.logging.error',
    'tf.logging.fatal':
        'tf.compat.v1.logging.fatal',
    'tf.logging.flush':
        'tf.compat.v1.logging.flush',
    'tf.logging.get_verbosity':
        'tf.compat.v1.logging.get_verbosity',
    'tf.logging.info':
        'tf.compat.v1.logging.info',
    'tf.logging.log':
        'tf.compat.v1.logging.log',
    'tf.logging.log_every_n':
        'tf.compat.v1.logging.log_every_n',
    'tf.logging.log_first_n':
        'tf.compat.v1.logging.log_first_n',
    'tf.logging.log_if':
        'tf.compat.v1.logging.log_if',
    'tf.logging.set_verbosity':
        'tf.compat.v1.logging.set_verbosity',
    'tf.logging.vlog':
        'tf.compat.v1.logging.vlog',
    'tf.logging.warn':
        'tf.compat.v1.logging.warn',
    'tf.logging.warning':
        'tf.compat.v1.logging.warning',
    'tf.logical_xor':
        'tf.math.logical_xor',
    'tf.losses.Reduction':
        'tf.compat.v1.losses.Reduction',
    'tf.losses.absolute_difference':
        'tf.compat.v1.losses.absolute_difference',
    'tf.losses.add_loss':
        'tf.compat.v1.losses.add_loss',
    'tf.losses.compute_weighted_loss':
        'tf.compat.v1.losses.compute_weighted_loss',
    'tf.losses.cosine_distance':
        'tf.compat.v1.losses.cosine_distance',
    'tf.losses.get_losses':
        'tf.compat.v1.losses.get_losses',
    'tf.losses.get_regularization_loss':
        'tf.compat.v1.losses.get_regularization_loss',
    'tf.losses.get_regularization_losses':
        'tf.compat.v1.losses.get_regularization_losses',
    'tf.losses.get_total_loss':
        'tf.compat.v1.losses.get_total_loss',
    'tf.losses.hinge_loss':
        'tf.compat.v1.losses.hinge_loss',
    'tf.losses.huber_loss':
        'tf.compat.v1.losses.huber_loss',
    'tf.losses.log_loss':
        'tf.compat.v1.losses.log_loss',
    'tf.losses.mean_pairwise_squared_error':
        'tf.compat.v1.losses.mean_pairwise_squared_error',
    'tf.losses.mean_squared_error':
        'tf.compat.v1.losses.mean_squared_error',
    'tf.losses.sigmoid_cross_entropy':
        'tf.compat.v1.losses.sigmoid_cross_entropy',
    'tf.losses.softmax_cross_entropy':
        'tf.compat.v1.losses.softmax_cross_entropy',
    'tf.losses.sparse_softmax_cross_entropy':
        'tf.compat.v1.losses.sparse_softmax_cross_entropy',
    'tf.make_template':
        'tf.compat.v1.make_template',
    'tf.manip.gather_nd':
        'tf.gather_nd',
    'tf.manip.reshape':
        'tf.reshape',
    'tf.manip.reverse':
        'tf.reverse',
    'tf.manip.roll':
        'tf.roll',
    'tf.manip.scatter_nd':
        'tf.scatter_nd',
    'tf.manip.space_to_batch_nd':
        'tf.space_to_batch_nd',
    'tf.manip.tile':
        'tf.tile',
    'tf.matching_files':
        'tf.io.matching_files',
    'tf.matrix_band_part':
        'tf.linalg.band_part',
    'tf.matrix_determinant':
        'tf.linalg.det',
    'tf.matrix_diag':
        'tf.linalg.diag',
    'tf.matrix_diag_part':
        'tf.linalg.diag_part',
    'tf.matrix_inverse':
        'tf.linalg.inv',
    'tf.matrix_set_diag':
        'tf.linalg.set_diag',
    'tf.matrix_solve':
        'tf.linalg.solve',
    'tf.matrix_solve_ls':
        'tf.linalg.lstsq',
    'tf.matrix_transpose':
        'tf.linalg.matrix_transpose',
    'tf.matrix_triangular_solve':
        'tf.linalg.triangular_solve',
    'tf.metrics.accuracy':
        'tf.compat.v1.metrics.accuracy',
    'tf.metrics.auc':
        'tf.compat.v1.metrics.auc',
    'tf.metrics.average_precision_at_k':
        'tf.compat.v1.metrics.average_precision_at_k',
    'tf.metrics.false_negatives':
        'tf.compat.v1.metrics.false_negatives',
    'tf.metrics.false_negatives_at_thresholds':
        'tf.compat.v1.metrics.false_negatives_at_thresholds',
    'tf.metrics.false_positives':
        'tf.compat.v1.metrics.false_positives',
    'tf.metrics.false_positives_at_thresholds':
        'tf.compat.v1.metrics.false_positives_at_thresholds',
    'tf.metrics.mean':
        'tf.compat.v1.metrics.mean',
    'tf.metrics.mean_absolute_error':
        'tf.compat.v1.metrics.mean_absolute_error',
    'tf.metrics.mean_cosine_distance':
        'tf.compat.v1.metrics.mean_cosine_distance',
    'tf.metrics.mean_iou':
        'tf.compat.v1.metrics.mean_iou',
    'tf.metrics.mean_per_class_accuracy':
        'tf.compat.v1.metrics.mean_per_class_accuracy',
    'tf.metrics.mean_relative_error':
        'tf.compat.v1.metrics.mean_relative_error',
    'tf.metrics.mean_squared_error':
        'tf.compat.v1.metrics.mean_squared_error',
    'tf.metrics.mean_tensor':
        'tf.compat.v1.metrics.mean_tensor',
    'tf.metrics.percentage_below':
        'tf.compat.v1.metrics.percentage_below',
    'tf.metrics.precision':
        'tf.compat.v1.metrics.precision',
    'tf.metrics.precision_at_k':
        'tf.compat.v1.metrics.precision_at_k',
    'tf.metrics.precision_at_thresholds':
        'tf.compat.v1.metrics.precision_at_thresholds',
    'tf.metrics.precision_at_top_k':
        'tf.compat.v1.metrics.precision_at_top_k',
    'tf.metrics.recall':
        'tf.compat.v1.metrics.recall',
    'tf.metrics.recall_at_k':
        'tf.compat.v1.metrics.recall_at_k',
    'tf.metrics.recall_at_thresholds':
        'tf.compat.v1.metrics.recall_at_thresholds',
    'tf.metrics.recall_at_top_k':
        'tf.compat.v1.metrics.recall_at_top_k',
    'tf.metrics.root_mean_squared_error':
        'tf.compat.v1.metrics.root_mean_squared_error',
    'tf.metrics.sensitivity_at_specificity':
        'tf.compat.v1.metrics.sensitivity_at_specificity',
    'tf.metrics.sparse_average_precision_at_k':
        'tf.compat.v1.metrics.sparse_average_precision_at_k',
    'tf.metrics.sparse_precision_at_k':
        'tf.compat.v1.metrics.sparse_precision_at_k',
    'tf.metrics.specificity_at_sensitivity':
        'tf.compat.v1.metrics.specificity_at_sensitivity',
    'tf.metrics.true_negatives':
        'tf.compat.v1.metrics.true_negatives',
    'tf.metrics.true_negatives_at_thresholds':
        'tf.compat.v1.metrics.true_negatives_at_thresholds',
    'tf.metrics.true_positives':
        'tf.compat.v1.metrics.true_positives',
    'tf.metrics.true_positives_at_thresholds':
        'tf.compat.v1.metrics.true_positives_at_thresholds',
    'tf.min_max_variable_partitioner':
        'tf.compat.v1.min_max_variable_partitioner',
    'tf.mixed_precision.DynamicLossScale':
        'tf.compat.v1.mixed_precision.DynamicLossScale',
    'tf.mixed_precision.FixedLossScale':
        'tf.compat.v1.mixed_precision.FixedLossScale',
    'tf.mixed_precision.LossScale':
        'tf.compat.v1.mixed_precision.LossScale',
    'tf.mixed_precision.MixedPrecisionLossScaleOptimizer':
        'tf.compat.v1.mixed_precision.MixedPrecisionLossScaleOptimizer',
    'tf.mixed_precision.disable_mixed_precision_graph_rewrite':
        'tf.compat.v1.mixed_precision.disable_mixed_precision_graph_rewrite',
    'tf.mixed_precision.enable_mixed_precision_graph_rewrite':
        'tf.compat.v1.mixed_precision.enable_mixed_precision_graph_rewrite',
    'tf.mixed_precision.experimental.DynamicLossScale':
        'tf.compat.v1.mixed_precision.experimental.DynamicLossScale',
    'tf.mixed_precision.experimental.FixedLossScale':
        'tf.compat.v1.mixed_precision.experimental.FixedLossScale',
    'tf.mixed_precision.experimental.LossScale':
        'tf.compat.v1.mixed_precision.experimental.LossScale',
    'tf.mod':
        'tf.math.floormod',
    'tf.model_variables':
        'tf.compat.v1.model_variables',
    'tf.moving_average_variables':
        'tf.compat.v1.moving_average_variables',
    'tf.nn.avg_pool_v2':
        'tf.nn.avg_pool',
    'tf.nn.bidirectional_dynamic_rnn':
        'tf.compat.v1.nn.bidirectional_dynamic_rnn',
    'tf.nn.conv2d_backprop_filter':
        'tf.compat.v1.nn.conv2d_backprop_filter',
    'tf.nn.conv3d_backprop_filter':
        'tf.compat.v1.nn.conv3d_backprop_filter',
    'tf.nn.conv3d_backprop_filter_v2':
        'tf.compat.v1.nn.conv3d_backprop_filter_v2',
    'tf.nn.ctc_beam_search_decoder_v2':
        'tf.nn.ctc_beam_search_decoder',
    'tf.nn.ctc_loss_v2':
        'tf.compat.v1.nn.ctc_loss_v2',
    'tf.nn.depthwise_conv2d_native':
        'tf.compat.v1.nn.depthwise_conv2d_native',
    'tf.nn.depthwise_conv2d_native_backprop_filter':
        'tf.nn.depthwise_conv2d_backprop_filter',
    'tf.nn.depthwise_conv2d_native_backprop_input':
        'tf.nn.depthwise_conv2d_backprop_input',
    'tf.nn.dynamic_rnn':
        'tf.compat.v1.nn.dynamic_rnn',
    'tf.nn.log_uniform_candidate_sampler':
        'tf.random.log_uniform_candidate_sampler',
    'tf.nn.max_pool_v2':
        'tf.nn.max_pool',
    'tf.nn.quantized_avg_pool':
        'tf.compat.v1.nn.quantized_avg_pool',
    'tf.nn.quantized_conv2d':
        'tf.compat.v1.nn.quantized_conv2d',
    'tf.nn.quantized_max_pool':
        'tf.compat.v1.nn.quantized_max_pool',
    'tf.nn.quantized_relu_x':
        'tf.compat.v1.nn.quantized_relu_x',
    'tf.nn.raw_rnn':
        'tf.compat.v1.nn.raw_rnn',
    'tf.nn.relu_layer':
        'tf.compat.v1.nn.relu_layer',
    'tf.nn.rnn_cell.BasicLSTMCell':
        'tf.compat.v1.nn.rnn_cell.BasicLSTMCell',
    'tf.nn.rnn_cell.BasicRNNCell':
        'tf.compat.v1.nn.rnn_cell.BasicRNNCell',
    'tf.nn.rnn_cell.DeviceWrapper':
        'tf.compat.v1.nn.rnn_cell.DeviceWrapper',
    'tf.nn.rnn_cell.DropoutWrapper':
        'tf.compat.v1.nn.rnn_cell.DropoutWrapper',
    'tf.nn.rnn_cell.GRUCell':
        'tf.compat.v1.nn.rnn_cell.GRUCell',
    'tf.nn.rnn_cell.LSTMCell':
        'tf.compat.v1.nn.rnn_cell.LSTMCell',
    'tf.nn.rnn_cell.LSTMStateTuple':
        'tf.compat.v1.nn.rnn_cell.LSTMStateTuple',
    'tf.nn.rnn_cell.MultiRNNCell':
        'tf.compat.v1.nn.rnn_cell.MultiRNNCell',
    'tf.nn.rnn_cell.RNNCell':
        'tf.compat.v1.nn.rnn_cell.RNNCell',
    'tf.nn.rnn_cell.ResidualWrapper':
        'tf.compat.v1.nn.rnn_cell.ResidualWrapper',
    'tf.nn.static_bidirectional_rnn':
        'tf.compat.v1.nn.static_bidirectional_rnn',
    'tf.nn.static_rnn':
        'tf.compat.v1.nn.static_rnn',
    'tf.nn.static_state_saving_rnn':
        'tf.compat.v1.nn.static_state_saving_rnn',
    'tf.nn.uniform_candidate_sampler':
        'tf.random.uniform_candidate_sampler',
    'tf.nn.xw_plus_b':
        'tf.compat.v1.nn.xw_plus_b',
    'tf.no_regularizer':
        'tf.compat.v1.no_regularizer',
    'tf.op_scope':
        'tf.compat.v1.op_scope',
    'tf.parse_single_sequence_example':
        'tf.io.parse_single_sequence_example',
    'tf.parse_tensor':
        'tf.io.parse_tensor',
    'tf.placeholder':
        'tf.compat.v1.placeholder',
    'tf.placeholder_with_default':
        'tf.compat.v1.placeholder_with_default',
    'tf.polygamma':
        'tf.math.polygamma',
    'tf.profiler.AdviceProto':
        'tf.compat.v1.profiler.AdviceProto',
    'tf.profiler.GraphNodeProto':
        'tf.compat.v1.profiler.GraphNodeProto',
    'tf.profiler.MultiGraphNodeProto':
        'tf.compat.v1.profiler.MultiGraphNodeProto',
    'tf.profiler.OpLogProto':
        'tf.compat.v1.profiler.OpLogProto',
    'tf.profiler.ProfileOptionBuilder':
        'tf.compat.v1.profiler.ProfileOptionBuilder',
    'tf.profiler.Profiler':
        'tf.compat.v1.profiler.Profiler',
    'tf.profiler.advise':
        'tf.compat.v1.profiler.advise',
    'tf.profiler.profile':
        'tf.compat.v1.profiler.profile',
    'tf.profiler.write_op_log':
        'tf.compat.v1.profiler.write_op_log',
    'tf.py_func':
        'tf.compat.v1.py_func',
    'tf.python_io.TFRecordCompressionType':
        'tf.compat.v1.python_io.TFRecordCompressionType',
    'tf.python_io.TFRecordOptions':
        'tf.io.TFRecordOptions',
    'tf.python_io.TFRecordWriter':
        'tf.io.TFRecordWriter',
    'tf.python_io.tf_record_iterator':
        'tf.compat.v1.python_io.tf_record_iterator',
    'tf.qr':
        'tf.linalg.qr',
    'tf.quantize':
        'tf.quantization.quantize',
    'tf.quantized_concat':
        'tf.quantization.quantized_concat',
    'tf.ragged.RaggedTensorValue':
        'tf.compat.v1.ragged.RaggedTensorValue',
    'tf.ragged.constant_value':
        'tf.compat.v1.ragged.constant_value',
    'tf.ragged.placeholder':
        'tf.compat.v1.ragged.placeholder',
    'tf.random.get_seed':
        'tf.compat.v1.random.get_seed',
    'tf.random.set_random_seed':
        'tf.compat.v1.random.set_random_seed',
    'tf.random_crop':
        'tf.image.random_crop',
    'tf.random_gamma':
        'tf.random.gamma',
    'tf.random_normal':
        'tf.random.normal',
    'tf.random_poisson':
        'tf.random.poisson',
    'tf.random_shuffle':
        'tf.random.shuffle',
    'tf.random_uniform':
        'tf.random.uniform',
    'tf.read_file':
        'tf.io.read_file',
    'tf.real':
        'tf.math.real',
    'tf.reciprocal':
        'tf.math.reciprocal',
    'tf.regex_replace':
        'tf.strings.regex_replace',
    'tf.report_uninitialized_variables':
        'tf.compat.v1.report_uninitialized_variables',
    'tf.reset_default_graph':
        'tf.compat.v1.reset_default_graph',
    'tf.resource_loader.get_data_files_path':
        'tf.compat.v1.resource_loader.get_data_files_path',
    'tf.resource_loader.get_path_to_datafile':
        'tf.compat.v1.resource_loader.get_path_to_datafile',
    'tf.resource_loader.get_root_dir_with_all_resources':
        'tf.compat.v1.resource_loader.get_root_dir_with_all_resources',
    'tf.resource_loader.load_resource':
        'tf.compat.v1.resource_loader.load_resource',
    'tf.resource_loader.readahead_file_path':
        'tf.compat.v1.resource_loader.readahead_file_path',
    'tf.resource_variables_enabled':
        'tf.compat.v1.resource_variables_enabled',
    'tf.reverse_v2':
        'tf.reverse',
    'tf.rint':
        'tf.math.rint',
    'tf.rsqrt':
        'tf.math.rsqrt',
    'tf.saved_model.Builder':
        'tf.compat.v1.saved_model.Builder',
    'tf.saved_model.LEGACY_INIT_OP_KEY':
        'tf.compat.v1.saved_model.LEGACY_INIT_OP_KEY',
    'tf.saved_model.MAIN_OP_KEY':
        'tf.compat.v1.saved_model.MAIN_OP_KEY',
    'tf.saved_model.build_signature_def':
        'tf.compat.v1.saved_model.build_signature_def',
    'tf.saved_model.build_tensor_info':
        'tf.compat.v1.saved_model.build_tensor_info',
    'tf.saved_model.builder.SavedModelBuilder':
        'tf.compat.v1.saved_model.builder.SavedModelBuilder',
    'tf.saved_model.classification_signature_def':
        'tf.compat.v1.saved_model.classification_signature_def',
    'tf.saved_model.constants.ASSETS_DIRECTORY':
        'tf.saved_model.ASSETS_DIRECTORY',
    'tf.saved_model.constants.ASSETS_KEY':
        'tf.saved_model.ASSETS_KEY',
    'tf.saved_model.constants.DEBUG_DIRECTORY':
        'tf.saved_model.DEBUG_DIRECTORY',
    'tf.saved_model.constants.DEBUG_INFO_FILENAME_PB':
        'tf.saved_model.DEBUG_INFO_FILENAME_PB',
    'tf.saved_model.constants.LEGACY_INIT_OP_KEY':
        'tf.compat.v1.saved_model.constants.LEGACY_INIT_OP_KEY',
    'tf.saved_model.constants.MAIN_OP_KEY':
        'tf.compat.v1.saved_model.constants.MAIN_OP_KEY',
    'tf.saved_model.constants.SAVED_MODEL_FILENAME_PB':
        'tf.saved_model.SAVED_MODEL_FILENAME_PB',
    'tf.saved_model.constants.SAVED_MODEL_FILENAME_PBTXT':
        'tf.saved_model.SAVED_MODEL_FILENAME_PBTXT',
    'tf.saved_model.constants.SAVED_MODEL_SCHEMA_VERSION':
        'tf.saved_model.SAVED_MODEL_SCHEMA_VERSION',
    'tf.saved_model.constants.VARIABLES_DIRECTORY':
        'tf.saved_model.VARIABLES_DIRECTORY',
    'tf.saved_model.constants.VARIABLES_FILENAME':
        'tf.saved_model.VARIABLES_FILENAME',
    'tf.saved_model.experimental.save':
        'tf.saved_model.save',
    'tf.saved_model.get_tensor_from_tensor_info':
        'tf.compat.v1.saved_model.get_tensor_from_tensor_info',
    'tf.saved_model.is_valid_signature':
        'tf.compat.v1.saved_model.is_valid_signature',
    'tf.saved_model.loader.maybe_saved_model_directory':
        'tf.saved_model.contains_saved_model',
    'tf.saved_model.main_op.main_op':
        'tf.compat.v1.saved_model.main_op.main_op',
    'tf.saved_model.main_op.main_op_with_restore':
        'tf.compat.v1.saved_model.main_op.main_op_with_restore',
    'tf.saved_model.main_op_with_restore':
        'tf.compat.v1.saved_model.main_op_with_restore',
    'tf.saved_model.maybe_saved_model_directory':
        'tf.saved_model.contains_saved_model',
    'tf.saved_model.predict_signature_def':
        'tf.compat.v1.saved_model.predict_signature_def',
    'tf.saved_model.regression_signature_def':
        'tf.compat.v1.saved_model.regression_signature_def',
    'tf.saved_model.signature_constants.CLASSIFY_INPUTS':
        'tf.saved_model.CLASSIFY_INPUTS',
    'tf.saved_model.signature_constants.CLASSIFY_METHOD_NAME':
        'tf.saved_model.CLASSIFY_METHOD_NAME',
    'tf.saved_model.signature_constants.CLASSIFY_OUTPUT_CLASSES':
        'tf.saved_model.CLASSIFY_OUTPUT_CLASSES',
    'tf.saved_model.signature_constants.CLASSIFY_OUTPUT_SCORES':
        'tf.saved_model.CLASSIFY_OUTPUT_SCORES',
    'tf.saved_model.signature_constants.DEFAULT_SERVING_SIGNATURE_DEF_KEY':
        'tf.saved_model.DEFAULT_SERVING_SIGNATURE_DEF_KEY',
    'tf.saved_model.signature_constants.PREDICT_INPUTS':
        'tf.saved_model.PREDICT_INPUTS',
    'tf.saved_model.signature_constants.PREDICT_METHOD_NAME':
        'tf.saved_model.PREDICT_METHOD_NAME',
    'tf.saved_model.signature_constants.PREDICT_OUTPUTS':
        'tf.saved_model.PREDICT_OUTPUTS',
    'tf.saved_model.signature_constants.REGRESS_INPUTS':
        'tf.saved_model.REGRESS_INPUTS',
    'tf.saved_model.signature_constants.REGRESS_METHOD_NAME':
        'tf.saved_model.REGRESS_METHOD_NAME',
    'tf.saved_model.signature_constants.REGRESS_OUTPUTS':
        'tf.saved_model.REGRESS_OUTPUTS',
    'tf.saved_model.signature_def_utils.MethodNameUpdater':
        'tf.compat.v1.saved_model.signature_def_utils.MethodNameUpdater',
    'tf.saved_model.signature_def_utils.build_signature_def':
        'tf.compat.v1.saved_model.signature_def_utils.build_signature_def',
    'tf.saved_model.signature_def_utils.classification_signature_def':
        'tf.compat.v1.saved_model.signature_def_utils.classification_signature_def',
    'tf.saved_model.signature_def_utils.is_valid_signature':
        'tf.compat.v1.saved_model.signature_def_utils.is_valid_signature',
    'tf.saved_model.signature_def_utils.predict_signature_def':
        'tf.compat.v1.saved_model.signature_def_utils.predict_signature_def',
    'tf.saved_model.signature_def_utils.regression_signature_def':
        'tf.compat.v1.saved_model.signature_def_utils.regression_signature_def',
    'tf.saved_model.simple_save':
        'tf.compat.v1.saved_model.simple_save',
    'tf.saved_model.tag_constants.GPU':
        'tf.saved_model.GPU',
    'tf.saved_model.tag_constants.SERVING':
        'tf.saved_model.SERVING',
    'tf.saved_model.tag_constants.TPU':
        'tf.saved_model.TPU',
    'tf.saved_model.tag_constants.TRAINING':
        'tf.saved_model.TRAINING',
    'tf.saved_model.utils.build_tensor_info':
        'tf.compat.v1.saved_model.utils.build_tensor_info',
    'tf.saved_model.utils.get_tensor_from_tensor_info':
        'tf.compat.v1.saved_model.utils.get_tensor_from_tensor_info',
    'tf.scatter_add':
        'tf.compat.v1.scatter_add',
    'tf.scatter_div':
        'tf.compat.v1.scatter_div',
    'tf.scatter_max':
        'tf.compat.v1.scatter_max',
    'tf.scatter_min':
        'tf.compat.v1.scatter_min',
    'tf.scatter_mul':
        'tf.compat.v1.scatter_mul',
    'tf.scatter_nd_add':
        'tf.compat.v1.scatter_nd_add',
    'tf.scatter_nd_sub':
        'tf.compat.v1.scatter_nd_sub',
    'tf.scatter_nd_update':
        'tf.compat.v1.scatter_nd_update',
    'tf.scatter_sub':
        'tf.compat.v1.scatter_sub',
    'tf.scatter_update':
        'tf.compat.v1.scatter_update',
    'tf.segment_max':
        'tf.math.segment_max',
    'tf.segment_mean':
        'tf.math.segment_mean',
    'tf.segment_min':
        'tf.math.segment_min',
    'tf.segment_prod':
        'tf.math.segment_prod',
    'tf.segment_sum':
        'tf.math.segment_sum',
    'tf.self_adjoint_eig':
        'tf.linalg.eigh',
    'tf.self_adjoint_eigvals':
        'tf.linalg.eigvalsh',
    'tf.serialize_many_sparse':
        'tf.io.serialize_many_sparse',
    'tf.serialize_sparse':
        'tf.io.serialize_sparse',
    'tf.serialize_tensor':
        'tf.io.serialize_tensor',
    'tf.set_random_seed':
        'tf.compat.v1.set_random_seed',
    'tf.setdiff1d':
        'tf.compat.v1.setdiff1d',
    'tf.sets.set_difference':
        'tf.sets.difference',
    'tf.sets.set_intersection':
        'tf.sets.intersection',
    'tf.sets.set_size':
        'tf.sets.size',
    'tf.sets.set_union':
        'tf.sets.union',
    'tf.space_to_depth':
        'tf.nn.space_to_depth',
    'tf.sparse.SparseConditionalAccumulator':
        'tf.compat.v1.sparse.SparseConditionalAccumulator',
    'tf.sparse.matmul':
        'tf.sparse.sparse_dense_matmul',
    'tf.sparse.merge':
        'tf.compat.v1.sparse.merge',
    'tf.sparse.placeholder':
        'tf.compat.v1.sparse.placeholder',
    'tf.sparse.reduce_max_sparse':
        'tf.compat.v1.sparse.reduce_max_sparse',
    'tf.sparse.reduce_sum_sparse':
        'tf.compat.v1.sparse.reduce_sum_sparse',
    'tf.sparse_add':
        'tf.sparse.add',
    'tf.sparse_concat':
        'tf.sparse.concat',
    'tf.sparse_fill_empty_rows':
        'tf.sparse.fill_empty_rows',
    'tf.sparse_mask':
        'tf.sparse.mask',
    'tf.sparse_maximum':
        'tf.sparse.maximum',
    'tf.sparse_merge':
        'tf.compat.v1.sparse_merge',
    'tf.sparse_minimum':
        'tf.sparse.minimum',
    'tf.sparse_placeholder':
        'tf.compat.v1.sparse_placeholder',
    'tf.sparse_reduce_max':
        'tf.sparse.reduce_max',
    'tf.sparse_reduce_max_sparse':
        'tf.compat.v1.sparse_reduce_max_sparse',
    'tf.sparse_reduce_sum':
        'tf.sparse.reduce_sum',
    'tf.sparse_reduce_sum_sparse':
        'tf.compat.v1.sparse_reduce_sum_sparse',
    'tf.sparse_reorder':
        'tf.sparse.reorder',
    'tf.sparse_reset_shape':
        'tf.sparse.reset_shape',
    'tf.sparse_reshape':
        'tf.sparse.reshape',
    'tf.sparse_retain':
        'tf.sparse.retain',
    'tf.sparse_segment_mean':
        'tf.sparse.segment_mean',
    'tf.sparse_segment_sqrt_n':
        'tf.sparse.segment_sqrt_n',
    'tf.sparse_segment_sum':
        'tf.sparse.segment_sum',
    'tf.sparse_slice':
        'tf.sparse.slice',
    'tf.sparse_softmax':
        'tf.sparse.softmax',
    'tf.sparse_split':
        'tf.sparse.split',
    'tf.sparse_tensor_dense_matmul':
        'tf.sparse.sparse_dense_matmul',
    'tf.sparse_tensor_to_dense':
        'tf.sparse.to_dense',
    'tf.sparse_to_dense':
        'tf.compat.v1.sparse_to_dense',
    'tf.sparse_to_indicator':
        'tf.sparse.to_indicator',
    'tf.sparse_transpose':
        'tf.sparse.transpose',
    'tf.spectral.dct':
        'tf.signal.dct',
    'tf.spectral.fft':
        'tf.signal.fft',
    'tf.spectral.fft2d':
        'tf.signal.fft2d',
    'tf.spectral.fft3d':
        'tf.signal.fft3d',
    'tf.spectral.idct':
        'tf.signal.idct',
    'tf.spectral.ifft':
        'tf.signal.ifft',
    'tf.spectral.ifft2d':
        'tf.signal.ifft2d',
    'tf.spectral.ifft3d':
        'tf.signal.ifft3d',
    'tf.spectral.irfft':
        'tf.signal.irfft',
    'tf.spectral.irfft2d':
        'tf.signal.irfft2d',
    'tf.spectral.irfft3d':
        'tf.signal.irfft3d',
    'tf.spectral.rfft':
        'tf.signal.rfft',
    'tf.spectral.rfft2d':
        'tf.signal.rfft2d',
    'tf.spectral.rfft3d':
        'tf.signal.rfft3d',
    'tf.squared_difference':
        'tf.math.squared_difference',
    'tf.string_join':
        'tf.strings.join',
    'tf.string_strip':
        'tf.strings.strip',
    'tf.string_to_hash_bucket_fast':
        'tf.strings.to_hash_bucket_fast',
    'tf.string_to_hash_bucket_strong':
        'tf.strings.to_hash_bucket_strong',
    'tf.summary.Event':
        'tf.compat.v1.summary.Event',
    'tf.summary.FileWriter':
        'tf.compat.v1.summary.FileWriter',
    'tf.summary.FileWriterCache':
        'tf.compat.v1.summary.FileWriterCache',
    'tf.summary.SessionLog':
        'tf.compat.v1.summary.SessionLog',
    'tf.summary.Summary':
        'tf.compat.v1.summary.Summary',
    'tf.summary.SummaryDescription':
        'tf.compat.v1.summary.SummaryDescription',
    'tf.summary.TaggedRunMetadata':
        'tf.compat.v1.summary.TaggedRunMetadata',
    'tf.summary.all_v2_summary_ops':
        'tf.compat.v1.summary.all_v2_summary_ops',
    'tf.summary.get_summary_description':
        'tf.compat.v1.summary.get_summary_description',
    'tf.summary.initialize':
        'tf.compat.v1.summary.initialize',
    'tf.summary.merge':
        'tf.compat.v1.summary.merge',
    'tf.summary.merge_all':
        'tf.compat.v1.summary.merge_all',
    'tf.summary.tensor_summary':
        'tf.compat.v1.summary.tensor_summary',
    'tf.svd':
        'tf.linalg.svd',
    'tf.tables_initializer':
        'tf.compat.v1.tables_initializer',
    'tf.tensor_scatter_add':
        'tf.tensor_scatter_nd_add',
    'tf.tensor_scatter_sub':
        'tf.tensor_scatter_nd_sub',
    'tf.tensor_scatter_update':
        'tf.tensor_scatter_nd_update',
    'tf.test.StubOutForTesting':
        'tf.compat.v1.test.StubOutForTesting',
    'tf.test.compute_gradient_error':
        'tf.compat.v1.test.compute_gradient_error',
    'tf.test.get_temp_dir':
        'tf.compat.v1.test.get_temp_dir',
    'tf.test.mock':
        'tf.compat.v1.test.mock',
    'tf.test.test_src_dir_path':
        'tf.compat.v1.test.test_src_dir_path',
    'tf.to_bfloat16':
        'tf.compat.v1.to_bfloat16',
    'tf.to_complex128':
        'tf.compat.v1.to_complex128',
    'tf.to_complex64':
        'tf.compat.v1.to_complex64',
    'tf.to_double':
        'tf.compat.v1.to_double',
    'tf.to_float':
        'tf.compat.v1.to_float',
    'tf.to_int32':
        'tf.compat.v1.to_int32',
    'tf.to_int64':
        'tf.compat.v1.to_int64',
    'tf.tpu.CrossShardOptimizer':
        'tf.compat.v1.tpu.CrossShardOptimizer',
    'tf.tpu.PaddingSpec':
        'tf.compat.v1.tpu.PaddingSpec',
    'tf.tpu.batch_parallel':
        'tf.compat.v1.tpu.batch_parallel',
    'tf.tpu.bfloat16_scope':
        'tf.compat.v1.tpu.bfloat16_scope',
    'tf.tpu.core':
        'tf.compat.v1.tpu.core',
    'tf.tpu.cross_replica_sum':
        'tf.compat.v1.tpu.cross_replica_sum',
    'tf.tpu.experimental.AdagradParameters':
        'tf.compat.v1.tpu.experimental.AdagradParameters',
    'tf.tpu.experimental.AdamParameters':
        'tf.compat.v1.tpu.experimental.AdamParameters',
    'tf.tpu.experimental.FtrlParameters':
        'tf.compat.v1.tpu.experimental.FtrlParameters',
    'tf.tpu.experimental.StochasticGradientDescentParameters':
        'tf.compat.v1.tpu.experimental.StochasticGradientDescentParameters',
    'tf.tpu.experimental.embedding_column':
        'tf.compat.v1.tpu.experimental.embedding_column',
    'tf.tpu.experimental.shared_embedding_columns':
        'tf.compat.v1.tpu.experimental.shared_embedding_columns',
    'tf.tpu.initialize_system':
        'tf.compat.v1.tpu.initialize_system',
    'tf.tpu.outside_compilation':
        'tf.compat.v1.tpu.outside_compilation',
    'tf.tpu.replicate':
        'tf.compat.v1.tpu.replicate',
    'tf.tpu.rewrite':
        'tf.compat.v1.tpu.rewrite',
    'tf.tpu.shard':
        'tf.compat.v1.tpu.shard',
    'tf.tpu.shutdown_system':
        'tf.compat.v1.tpu.shutdown_system',
    'tf.trace':
        'tf.linalg.trace',
    'tf.train.AdadeltaOptimizer':
        'tf.compat.v1.train.AdadeltaOptimizer',
    'tf.train.AdagradDAOptimizer':
        'tf.compat.v1.train.AdagradDAOptimizer',
    'tf.train.AdagradOptimizer':
        'tf.compat.v1.train.AdagradOptimizer',
    'tf.train.AdamOptimizer':
        'tf.compat.v1.train.AdamOptimizer',
    'tf.train.CheckpointSaverHook':
        'tf.compat.v1.train.CheckpointSaverHook',
    'tf.train.CheckpointSaverListener':
        'tf.compat.v1.train.CheckpointSaverListener',
    'tf.train.ChiefSessionCreator':
        'tf.compat.v1.train.ChiefSessionCreator',
    'tf.train.FeedFnHook':
        'tf.compat.v1.train.FeedFnHook',
    'tf.train.FinalOpsHook':
        'tf.compat.v1.train.FinalOpsHook',
    'tf.train.FtrlOptimizer':
        'tf.compat.v1.train.FtrlOptimizer',
    'tf.train.GlobalStepWaiterHook':
        'tf.compat.v1.train.GlobalStepWaiterHook',
    'tf.train.GradientDescentOptimizer':
        'tf.compat.v1.train.GradientDescentOptimizer',
    'tf.train.LoggingTensorHook':
        'tf.compat.v1.train.LoggingTensorHook',
    'tf.train.LooperThread':
        'tf.compat.v1.train.LooperThread',
    'tf.train.MomentumOptimizer':
        'tf.compat.v1.train.MomentumOptimizer',
    'tf.train.MonitoredSession':
        'tf.compat.v1.train.MonitoredSession',
    'tf.train.MonitoredTrainingSession':
        'tf.compat.v1.train.MonitoredTrainingSession',
    'tf.train.NanLossDuringTrainingError':
        'tf.compat.v1.train.NanLossDuringTrainingError',
    'tf.train.NanTensorHook':
        'tf.compat.v1.train.NanTensorHook',
    'tf.train.NewCheckpointReader':
        'tf.compat.v1.train.NewCheckpointReader',
    'tf.train.Optimizer':
        'tf.compat.v1.train.Optimizer',
    'tf.train.ProfilerHook':
        'tf.compat.v1.train.ProfilerHook',
    'tf.train.ProximalAdagradOptimizer':
        'tf.compat.v1.train.ProximalAdagradOptimizer',
    'tf.train.ProximalGradientDescentOptimizer':
        'tf.compat.v1.train.ProximalGradientDescentOptimizer',
    'tf.train.QueueRunner':
        'tf.compat.v1.train.QueueRunner',
    'tf.train.RMSPropOptimizer':
        'tf.compat.v1.train.RMSPropOptimizer',
    'tf.train.Saver':
        'tf.compat.v1.train.Saver',
    'tf.train.SaverDef':
        'tf.compat.v1.train.SaverDef',
    'tf.train.Scaffold':
        'tf.compat.v1.train.Scaffold',
    'tf.train.SecondOrStepTimer':
        'tf.compat.v1.train.SecondOrStepTimer',
    'tf.train.Server':
        'tf.distribute.Server',
    'tf.train.SessionCreator':
        'tf.compat.v1.train.SessionCreator',
    'tf.train.SessionManager':
        'tf.compat.v1.train.SessionManager',
    'tf.train.SessionRunArgs':
        'tf.compat.v1.train.SessionRunArgs',
    'tf.train.SessionRunContext':
        'tf.compat.v1.train.SessionRunContext',
    'tf.train.SessionRunHook':
        'tf.compat.v1.train.SessionRunHook',
    'tf.train.SessionRunValues':
        'tf.compat.v1.train.SessionRunValues',
    'tf.train.SingularMonitoredSession':
        'tf.compat.v1.train.SingularMonitoredSession',
    'tf.train.StepCounterHook':
        'tf.compat.v1.train.StepCounterHook',
    'tf.train.StopAtStepHook':
        'tf.compat.v1.train.StopAtStepHook',
    'tf.train.SummarySaverHook':
        'tf.compat.v1.train.SummarySaverHook',
    'tf.train.Supervisor':
        'tf.compat.v1.train.Supervisor',
    'tf.train.SyncReplicasOptimizer':
        'tf.compat.v1.train.SyncReplicasOptimizer',
    'tf.train.VocabInfo':
        'tf.compat.v1.train.VocabInfo',
    'tf.train.WorkerSessionCreator':
        'tf.compat.v1.train.WorkerSessionCreator',
    'tf.train.add_queue_runner':
        'tf.compat.v1.train.add_queue_runner',
    'tf.train.assert_global_step':
        'tf.compat.v1.train.assert_global_step',
    'tf.train.basic_train_loop':
        'tf.compat.v1.train.basic_train_loop',
    'tf.train.batch':
        'tf.compat.v1.train.batch',
    'tf.train.batch_join':
        'tf.compat.v1.train.batch_join',
    'tf.train.checkpoint_exists':
        'tf.compat.v1.train.checkpoint_exists',
    'tf.train.cosine_decay':
        'tf.compat.v1.train.cosine_decay',
    'tf.train.cosine_decay_restarts':
        'tf.compat.v1.train.cosine_decay_restarts',
    'tf.train.create_global_step':
        'tf.compat.v1.train.create_global_step',
    'tf.train.do_quantize_training_on_graphdef':
        'tf.compat.v1.train.do_quantize_training_on_graphdef',
    'tf.train.experimental.DynamicLossScale':
        'tf.compat.v1.train.experimental.DynamicLossScale',
    'tf.train.experimental.FixedLossScale':
        'tf.compat.v1.train.experimental.FixedLossScale',
    'tf.train.experimental.LossScale':
        'tf.compat.v1.train.experimental.LossScale',
    'tf.train.experimental.MixedPrecisionLossScaleOptimizer':
        'tf.compat.v1.train.experimental.MixedPrecisionLossScaleOptimizer',
    'tf.train.experimental.disable_mixed_precision_graph_rewrite':
        'tf.compat.v1.train.experimental.disable_mixed_precision_graph_rewrite',
    'tf.train.experimental.enable_mixed_precision_graph_rewrite':
        'tf.compat.v1.train.experimental.enable_mixed_precision_graph_rewrite',
    'tf.train.exponential_decay':
        'tf.compat.v1.train.exponential_decay',
    'tf.train.export_meta_graph':
        'tf.compat.v1.train.export_meta_graph',
    'tf.train.generate_checkpoint_state_proto':
        'tf.compat.v1.train.generate_checkpoint_state_proto',
    'tf.train.get_checkpoint_mtimes':
        'tf.compat.v1.train.get_checkpoint_mtimes',
    'tf.train.get_global_step':
        'tf.compat.v1.train.get_global_step',
    'tf.train.get_or_create_global_step':
        'tf.compat.v1.train.get_or_create_global_step',
    'tf.train.global_step':
        'tf.compat.v1.train.global_step',
    'tf.train.import_meta_graph':
        'tf.compat.v1.train.import_meta_graph',
    'tf.train.init_from_checkpoint':
        'tf.compat.v1.train.init_from_checkpoint',
    'tf.train.input_producer':
        'tf.compat.v1.train.input_producer',
    'tf.train.inverse_time_decay':
        'tf.compat.v1.train.inverse_time_decay',
    'tf.train.limit_epochs':
        'tf.compat.v1.train.limit_epochs',
    'tf.train.linear_cosine_decay':
        'tf.compat.v1.train.linear_cosine_decay',
    'tf.train.match_filenames_once':
        'tf.io.match_filenames_once',
    'tf.train.maybe_batch':
        'tf.compat.v1.train.maybe_batch',
    'tf.train.maybe_batch_join':
        'tf.compat.v1.train.maybe_batch_join',
    'tf.train.maybe_shuffle_batch':
        'tf.compat.v1.train.maybe_shuffle_batch',
    'tf.train.maybe_shuffle_batch_join':
        'tf.compat.v1.train.maybe_shuffle_batch_join',
    'tf.train.natural_exp_decay':
        'tf.compat.v1.train.natural_exp_decay',
    'tf.train.noisy_linear_cosine_decay':
        'tf.compat.v1.train.noisy_linear_cosine_decay',
    'tf.train.piecewise_constant':
        'tf.compat.v1.train.piecewise_constant',
    'tf.train.piecewise_constant_decay':
        'tf.compat.v1.train.piecewise_constant_decay',
    'tf.train.polynomial_decay':
        'tf.compat.v1.train.polynomial_decay',
    'tf.train.queue_runner.QueueRunner':
        'tf.compat.v1.train.queue_runner.QueueRunner',
    'tf.train.queue_runner.add_queue_runner':
        'tf.compat.v1.train.queue_runner.add_queue_runner',
    'tf.train.queue_runner.start_queue_runners':
        'tf.compat.v1.train.queue_runner.start_queue_runners',
    'tf.train.range_input_producer':
        'tf.compat.v1.train.range_input_producer',
    'tf.train.remove_checkpoint':
        'tf.compat.v1.train.remove_checkpoint',
    'tf.train.replica_device_setter':
        'tf.compat.v1.train.replica_device_setter',
    'tf.train.shuffle_batch':
        'tf.compat.v1.train.shuffle_batch',
    'tf.train.shuffle_batch_join':
        'tf.compat.v1.train.shuffle_batch_join',
    'tf.train.slice_input_producer':
        'tf.compat.v1.train.slice_input_producer',
    'tf.train.start_queue_runners':
        'tf.compat.v1.train.start_queue_runners',
    'tf.train.string_input_producer':
        'tf.compat.v1.train.string_input_producer',
    'tf.train.summary_iterator':
        'tf.compat.v1.train.summary_iterator',
    'tf.train.update_checkpoint_state':
        'tf.compat.v1.train.update_checkpoint_state',
    'tf.train.warm_start':
        'tf.compat.v1.train.warm_start',
    'tf.train.write_graph':
        'tf.io.write_graph',
    'tf.trainable_variables':
        'tf.compat.v1.trainable_variables',
    'tf.truncated_normal':
        'tf.random.truncated_normal',
    'tf.uniform_unit_scaling_initializer':
        'tf.compat.v1.uniform_unit_scaling_initializer',
    'tf.unsorted_segment_max':
        'tf.math.unsorted_segment_max',
    'tf.unsorted_segment_mean':
        'tf.math.unsorted_segment_mean',
    'tf.unsorted_segment_min':
        'tf.math.unsorted_segment_min',
    'tf.unsorted_segment_prod':
        'tf.math.unsorted_segment_prod',
    'tf.unsorted_segment_sqrt_n':
        'tf.math.unsorted_segment_sqrt_n',
    'tf.unsorted_segment_sum':
        'tf.math.unsorted_segment_sum',
    'tf.variable_axis_size_partitioner':
        'tf.compat.v1.variable_axis_size_partitioner',
    'tf.variable_op_scope':
        'tf.compat.v1.variable_op_scope',
    'tf.variable_scope':
        'tf.compat.v1.variable_scope',
    'tf.variables_initializer':
        'tf.compat.v1.variables_initializer',
    'tf.verify_tensor_all_finite':
        'tf.debugging.assert_all_finite',
    'tf.wrap_function':
        'tf.compat.v1.wrap_function',
    'tf.write_file':
        'tf.io.write_file',
    'tf.zeta':
        'tf.math.zeta'
}
