# Copyright 2018 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
# pylint: disable=line-too-long
"""List of renames to apply when converting from TF 1.0 to TF 2.0.

THIS FILE IS AUTOGENERATED: To update, please run:
  bazel run tensorflow/tools/compatibility/update:generate_v2_reorders_map
This file should be updated whenever a function is added to
self.reordered_function_names in tf_upgrade_v2.py.
"""
reorders = {
    'tf.argmax': [None, None, 'name', 'dimension', 'output_type'],
    'tf.argmin': [None, None, 'name', 'dimension', 'output_type'],
    'tf.batch_to_space': [None, 'crops', 'block_size', 'name', 'block_shape'],
    'tf.boolean_mask': [None, None, 'name', 'axis'],
    'tf.cond': [None, None, None, 'strict', 'name', 'fn1', 'fn2'],
    'tf.confusion_matrix': [None, None, None, 'dtype', 'name', 'weights'],
    'tf.convert_to_tensor': [None, None, 'name', 'preferred_dtype', 'dtype_hint'],
    'tf.data.experimental.RaggedTensorStructure': ['dtype', 'shape', 'ragged_rank'],
    'tf.data.experimental.SparseTensorStructure': ['dtype', 'shape'],
    'tf.data.experimental.TensorArrayStructure': ['dtype', 'element_shape', 'dynamic_size', 'infer_shape'],
    'tf.data.experimental.TensorStructure': ['dtype', 'shape'],
    'tf.debugging.assert_all_finite': ['t', 'msg', 'name', 'x', 'message'],
    'tf.decode_csv': [None, None, None, None, 'name', 'na_value', 'select_cols'],
    'tf.depth_to_space': [None, None, 'name', 'data_format'],
    'tf.feature_column.categorical_column_with_vocabulary_file': [None, None, None, 'num_oov_buckets', 'default_value', 'dtype'],
    'tf.gather_nd': [None, None, 'name', 'batch_dims'],
    'tf.gradients': [None, None, None, None, 'colocate_gradients_with_ops', 'gate_gradients', 'aggregation_method', 'stop_gradients', 'unconnected_gradients'],
    'tf.hessians': [None, None, 'name', 'colocate_gradients_with_ops', 'gate_gradients', 'aggregation_method'],
    'tf.image.sample_distorted_bounding_box': [None, None, None, 'seed2', 'min_object_covered', 'aspect_ratio_range', 'area_range', 'max_attempts', 'use_image_if_no_bounding_boxes', 'name'],
    'tf.initializers.uniform_unit_scaling': ['factor', 'seed', 'dtype'],
    'tf.io.decode_csv': [None, None, None, None, 'name', 'na_value', 'select_cols'],
    'tf.io.parse_example': [None, None, 'name', 'example_names'],
    'tf.io.parse_single_example': [None, None, 'name', 'example_names'],
    'tf.io.serialize_many_sparse': [None, 'name', 'out_type'],
    'tf.io.serialize_sparse': [None, 'name', 'out_type'],
    'tf.linalg.norm': [None, None, None, None, None, 'keep_dims'],
    'tf.manip.gather_nd': [None, None, 'name', 'batch_dims'],
    'tf.math.argmax': [None, None, 'name', 'dimension', 'output_type'],
    'tf.math.argmin': [None, None, 'name', 'dimension', 'output_type'],
    'tf.math.confusion_matrix': [None, None, None, 'dtype', 'name', 'weights'],
    'tf.math.in_top_k': ['predictions', 'targets', 'k', 'name'],
    'tf.math.reduce_all': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.math.reduce_any': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.math.reduce_logsumexp': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.math.reduce_max': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.math.reduce_mean': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.math.reduce_min': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.math.reduce_prod': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.math.reduce_sum': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.multinomial': [None, None, 'seed', 'name', 'output_dtype'],
    'tf.nn.avg_pool': ['value', 'ksize', 'strides', 'padding', 'data_format', 'name', 'input'],
    'tf.nn.avg_pool2d': ['value', 'ksize', 'strides', 'padding', 'data_format', 'name', 'input'],
    'tf.nn.conv1d': ['value', 'filters', 'stride', 'padding', 'use_cudnn_on_gpu', 'data_format', 'name', 'input', 'dilations'],
    'tf.nn.conv2d': [None, 'filter', 'strides', 'padding', 'use_cudnn_on_gpu', 'data_format', 'dilations', 'name', 'filters'],
    'tf.nn.conv2d_backprop_input': ['input_sizes', 'filter', 'out_backprop', 'strides', 'padding', 'use_cudnn_on_gpu', 'data_format', 'dilations', 'name', 'filters'],
    'tf.nn.convolution': [None, 'filter', 'padding', 'strides', 'dilation_rate', 'name', 'data_format', 'filters', 'dilations'],
    'tf.nn.crelu': [None, 'name', 'axis'],
    'tf.nn.ctc_beam_search_decoder': ['inputs', 'sequence_length', 'beam_width', 'top_paths', 'merge_repeated'],
    'tf.nn.depth_to_space': [None, None, 'name', 'data_format'],
    'tf.nn.depthwise_conv2d': [None, None, None, None, 'rate', 'name', 'data_format', 'dilations'],
    'tf.nn.embedding_lookup': [None, None, 'partition_strategy', 'name', 'validate_indices', 'max_norm'],
    'tf.nn.embedding_lookup_sparse': [None, None, None, 'partition_strategy', 'name', 'combiner', 'max_norm', 'allow_fast_lookup'],
    'tf.nn.fractional_avg_pool': ['value', 'pooling_ratio', 'pseudo_random', 'overlapping', 'deterministic', 'seed', 'seed2', 'name'],
    'tf.nn.fractional_max_pool': ['value', 'pooling_ratio', 'pseudo_random', 'overlapping', 'deterministic', 'seed', 'seed2', 'name'],
    'tf.nn.in_top_k': ['predictions', 'targets', 'k', 'name'],
    'tf.nn.max_pool': ['value', 'ksize', 'strides', 'padding', 'data_format', 'name', 'input'],
    'tf.nn.moments': [None, None, None, 'name', 'keep_dims', 'keepdims'],
    'tf.nn.pool': [None, None, None, 'padding', 'dilation_rate', 'strides', 'name', 'data_format', 'dilations'],
    'tf.nn.separable_conv2d': [None, None, None, None, None, 'rate', 'name', 'data_format', 'dilations'],
    'tf.nn.softmax_cross_entropy_with_logits': ['labels', 'logits', 'dim', 'name', 'axis'],
    'tf.nn.space_to_batch': [None, 'paddings', 'block_size', 'name', 'block_shape'],
    'tf.nn.space_to_depth': [None, None, 'name', 'data_format'],
    'tf.nn.weighted_moments': [None, None, None, 'name', 'keep_dims', 'keepdims'],
    'tf.norm': [None, None, None, None, None, 'keep_dims'],
    'tf.pad': [None, None, None, 'name', 'constant_values'],
    'tf.parse_example': [None, None, 'name', 'example_names'],
    'tf.parse_single_example': [None, None, 'name', 'example_names'],
    'tf.quantize_v2': [None, None, None, None, None, 'name', 'round_mode', 'narrow_range', 'axis', 'ensure_minimum_range'],
    'tf.random.multinomial': [None, None, 'seed', 'name', 'output_dtype'],
    'tf.random.poisson': ['lam', 'shape', 'dtype', 'seed', 'name'],
    'tf.random_poisson': ['lam', 'shape', 'dtype', 'seed', 'name'],
    'tf.reduce_all': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.reduce_any': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.reduce_join': [None, None, 'keep_dims', 'separator', 'name', 'reduction_indices', 'keepdims'],
    'tf.reduce_logsumexp': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.reduce_max': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.reduce_mean': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.reduce_min': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.reduce_prod': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.reduce_sum': [None, None, None, None, 'reduction_indices', 'keep_dims'],
    'tf.reverse_sequence': [None, None, None, None, None, 'seq_dim', 'batch_dim'],
    'tf.serialize_many_sparse': [None, 'name', 'out_type'],
    'tf.serialize_sparse': [None, 'name', 'out_type'],
    'tf.shape': [None, 'name', 'out_type'],
    'tf.size': [None, 'name', 'out_type'],
    'tf.space_to_batch': [None, 'paddings', 'block_size', 'name', 'block_shape'],
    'tf.space_to_depth': [None, None, 'name', 'data_format'],
    'tf.sparse.add': [None, None, None, 'thresh'],
    'tf.sparse.concat': [None, None, 'name', 'expand_nonconcat_dim', 'concat_dim', 'expand_nonconcat_dims'],
    'tf.sparse.reduce_max': [None, None, None, 'reduction_axes', 'keep_dims'],
    'tf.sparse.segment_mean': [None, None, None, 'name', 'num_segments', 'sparse_gradient'],
    'tf.sparse.segment_sqrt_n': [None, None, None, 'name', 'num_segments', 'sparse_gradient'],
    'tf.sparse.segment_sum': [None, None, None, 'name', 'num_segments', 'sparse_gradient'],
    'tf.sparse.split': ['keyword_required', 'sp_input', 'num_split', 'axis', 'name', 'split_dim'],
    'tf.sparse_add': [None, None, None, 'thresh'],
    'tf.sparse_concat': [None, None, 'name', 'expand_nonconcat_dim', 'concat_dim', 'expand_nonconcat_dims'],
    'tf.sparse_matmul': [None, None, None, None, 'a_is_sparse', 'b_is_sparse', 'name'],
    'tf.sparse_reduce_max': [None, None, None, 'reduction_axes', 'keep_dims'],
    'tf.sparse_segment_mean': [None, None, None, 'name', 'num_segments', 'sparse_gradient'],
    'tf.sparse_segment_sqrt_n': [None, None, None, 'name', 'num_segments', 'sparse_gradient'],
    'tf.sparse_segment_sum': [None, None, None, 'name', 'num_segments', 'sparse_gradient'],
    'tf.sparse_split': ['keyword_required', 'sp_input', 'num_split', 'axis', 'name', 'split_dim'],
    'tf.strings.length': [None, 'name', 'unit'],
    'tf.strings.reduce_join': [None, None, 'keep_dims', 'separator', 'name', 'reduction_indices', 'keepdims'],
    'tf.strings.substr': [None, None, None, 'name', 'unit'],
    'tf.substr': [None, None, None, 'name', 'unit'],
    'tf.test.assert_equal_graph_def': ['actual', 'expected', 'checkpoint_v2', 'hash_table_shared_name'],
    'tf.transpose': [None, None, 'name', 'conjugate'],
    'tf.tuple': [None, 'name', 'control_inputs'],
    'tf.uniform_unit_scaling_initializer': ['factor', 'seed', 'dtype'],
    'tf.verify_tensor_all_finite': ['t', 'msg', 'name', 'x', 'message'],
    'tf.while_loop': ['cond', 'body', 'loop_vars', 'shape_invariants', 'parallel_iterations', 'back_prop', 'swap_memory', 'name', 'maximum_iterations', 'return_same_structure']
}
