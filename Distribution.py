import os
import sys

import matplotlib.pyplot as plt

image_file_extensions = ['jpg', 'JPG', 'png', 'PNG']


class Class:
    def __init__(self, name):
        self.name = name
        self.categories = {}

    def add_category(self, name, amount):
        self.categories[name.lower()] = amount

    def create_plots(self):
        labels = [self.name.lower() + '_' + name for name in
                  self.categories.keys()]
        sizes = self.categories.values()

        fig, axs = plt.subplots(
            1, 2, width_ratios=[1, 2], figsize=(10, 5))
        fig.suptitle(f"{self.name} class distribution")
        fig.tight_layout(pad=1.0)
        # Pie chart, where the slices will be ordered and
        # plotted counter-clockwise:
        axs[0].pie(sizes, autopct='%1.1f%%', startangle=90)

        # get colors from the pie chart
        colors = [x.get_facecolor() for x in axs[0].patches]

        # Bar chart
        axs[1].bar(labels, sizes, color=colors)
        axs[1].grid(axis='y', linestyle='-.', linewidth=0.5)


def get_extension(file):
    return file.split('.')[-1]


def get_class_name(file):
    return file.split('_')[0]


def get_category_name(file):
    # get everything after the first underscore
    return file.split('_', 1)[1]


def show_classes(cls: dict[str, Class]):
    for klass in cls:
        cls[klass].create_plots()

    plt.show()


def get_number_of_images(folder: str):
    count = 0
    for name in os.listdir(folder):
        if os.path.isfile(os.path.join(folder, name)) and get_extension(
                name) in image_file_extensions:
            count += 1
    return count


def run_distribution(input_folder_name):
    """Run the distribution analysis."""
    if not os.path.exists(input_folder_name):
        print("The folder does not exist")
        return
    fd = os.listdir(input_folder_name)
    if len(fd) == 0:
        print("The folder is empty")
        return
    class_name = input_folder_name.split('/')[-1]
    klass = Class(class_name)
    for folder_name in fd:
        full_path = os.path.join(input_folder_name, folder_name)
        if os.path.isdir(full_path):
            klass.add_category(get_category_name(folder_name),
                               get_number_of_images(full_path))
    klass.create_plots()
    plt.savefig('image.jpg')


if __name__ == "__main__":
    if not len(sys.argv) == 2 or '-h' in sys.argv:
        print("Please enter a folder as parameter")
    else:
        run_distribution(sys.argv[1])
